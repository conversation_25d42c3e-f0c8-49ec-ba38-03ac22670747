import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:arepsalin/constants/app_colors.dart';

import '../../../constants/app_images.dart';
import 'custom_progress_indicator.dart';

class CustomImage extends StatelessWidget {
  final String? image;
  final double? height;
  final double? width;
  final bool addRadius;
  final double radius;
  final bool? withShadow;
  final bool? fromAssets;

  const CustomImage(
      {Key? key,
      required this.image,
      this.withShadow = true,
      this.height = 40,
      this.width = 40,
      this.addRadius = false,
      this.fromAssets = true,
      this.radius = 0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: withShadow!
          ? BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withOpacity(0.2),
                  blurRadius: 2,
                  spreadRadius: 0.2,
                ),
              ],
            )
          : null,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: !fromAssets!
            ? CachedNetworkImage(
                imageUrl: image!,
                fit: BoxFit.cover,
                height: height,
                width: width,
                placeholder: (ctx, text) => const CustomProgressIndicator(),
                errorWidget: (context, url, error) => Image.asset(
                  AppImages.error,
                  height: height,
                ),
              )
            : Image(
                image: AssetImage(image!),
                fit: BoxFit.cover,
                height: height,
                width: width,
              ),
      ),
    );
  }
}
