class GradeSummary {
  final Student student;
  final List<SemesterGrade> semesters;

  GradeSummary({
    required this.student,
    required this.semesters,
  });

  factory GradeSummary.fromJson(Map<String, dynamic> json) {
    return GradeSummary(
      student: Student.from<PERSON>son(json['student']),
      semesters: (json['semesters'] as List)
          .map((semester) => SemesterGrade.fromJson(semester))
          .toList(),
    );
  }
}

class Student {
  final int id;
  final String name;
  final String code;

  Student({
    required this.id,
    required this.name,
    required this.code,
  });

  factory Student.fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'],
      name: json['name'],
      code: json['code'],
    );
  }
}

class SemesterGrade {
  final int id;
  final String name;
  final int semesterNo;
  final List<SubjectGrade> subjects;

  SemesterGrade({
    required this.id,
    required this.name,
    required this.semesterNo,
    required this.subjects,
  });

  factory SemesterGrade.fromJson(Map<String, dynamic> json) {
    return SemesterGrade(
      id: json['id'],
      name: json['name'],
      semesterNo: json['semesterNo'],
      subjects: (json['subjects'] as List)
          .map((subject) => SubjectGrade.fromJson(subject))
          .toList(),
    );
  }

  // Calculate overall semester performance
  double get overallPercentage {
    if (subjects.isEmpty) return 0.0;
    
    double totalPercentage = 0.0;
    int subjectsWithGrades = 0;
    
    for (var subject in subjects) {
      if (subject.hasAnyGrades) {
        totalPercentage += subject.overallPercentage;
        subjectsWithGrades++;
      }
    }
    
    return subjectsWithGrades > 0 ? totalPercentage / subjectsWithGrades : 0.0;
  }
}

class SubjectGrade {
  final int id;
  final String name;
  final String code;
  final QuizGrade finalQuizzes;
  final QuizGrade weekQuizzes;

  SubjectGrade({
    required this.id,
    required this.name,
    required this.code,
    required this.finalQuizzes,
    required this.weekQuizzes,
  });

  factory SubjectGrade.fromJson(Map<String, dynamic> json) {
    return SubjectGrade(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      finalQuizzes: QuizGrade.fromJson(json['finalQuizzes']),
      weekQuizzes: QuizGrade.fromJson(json['weekQuizzes']),
    );
  }

  // Check if subject has any grades
  bool get hasAnyGrades => finalQuizzes.hasQuizzes || weekQuizzes.hasQuizzes;

  // Calculate overall subject percentage
  double get overallPercentage {
    if (!hasAnyGrades) return 0.0;
    
    double finalPercentage = finalQuizzes.hasQuizzes ? finalQuizzes.percentage : 0.0;
    double weekPercentage = weekQuizzes.hasQuizzes ? weekQuizzes.percentage : 0.0;
    
    if (finalQuizzes.hasQuizzes && weekQuizzes.hasQuizzes) {
      // Both grades available - weighted average (70% final, 30% weekly)
      return (finalPercentage * 0.7) + (weekPercentage * 0.3);
    } else if (finalQuizzes.hasQuizzes) {
      return finalPercentage;
    } else {
      return weekPercentage;
    }
  }
}

class QuizGrade {
  final int totalScore;
  final int finalScore;
  final bool hasQuizzes;

  QuizGrade({
    required this.totalScore,
    required this.finalScore,
    required this.hasQuizzes,
  });

  factory QuizGrade.fromJson(Map<String, dynamic> json) {
    return QuizGrade(
      totalScore: json['totalScore'] ?? 0,
      finalScore: json['finalScore'] ?? 0,
      hasQuizzes: json['hasQuizzes'] ?? false,
    );
  }

  // Calculate percentage
  double get percentage {
    if (!hasQuizzes || finalScore == 0) return 0.0;
    return (totalScore / finalScore) * 100;
  }

  // Get formatted score string
  String get scoreString {
    if (!hasQuizzes) return 'N/A';
    return '$totalScore/$finalScore';
  }

  // Get formatted percentage string
  String get percentageString {
    if (!hasQuizzes) return 'N/A';
    return '${percentage.toStringAsFixed(1)}%';
  }
}
