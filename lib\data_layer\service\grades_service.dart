import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:arepsalin/constants/variables.dart';
import 'package:arepsalin/data_layer/model/grade_model.dart';

import '../../managers/secure_storage.dart';


class GradesService {
  final SecureStorage _secureStorage = SecureStorage();

  Future<GradeSummary?> getStudentGrades() async {
    try {
      // Get access token
      final accessToken = await _secureStorage.getToken('access_token');
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      // Make API request
      final response = await http.get(
        Uri.parse('${Variables.baseUrl}/grades/student/summary'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        
        if (jsonResponse['data'] != null) {
          return GradeSummary.fromJson(jsonResponse['data']);
        } else {
          throw Exception('No data found in response');
        }
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized - Please login again');
      } else if (response.statusCode == 404) {
        throw Exception('No grades found');
      } else {
        throw Exception('Failed to load grades: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching grades: $e');
    }
  }
}
