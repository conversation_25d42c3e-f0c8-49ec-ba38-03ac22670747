import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:arepsalin/data_layer/provider/grades_provider.dart';
import 'package:arepsalin/data_layer/model/grade_model.dart';


import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../../translation/locale_keys.g.dart';

class GradesScreen extends StatefulWidget {
  const GradesScreen({super.key});

  @override
  State<GradesScreen> createState() => _GradesScreenState();
}

class _GradesScreenState extends State<GradesScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch grades when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<GradesProvider>(context, listen: false).fetchGrades();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.warmWhite,
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          LocaleKeys.grades.tr(),
          style: ThemeManager.semiBold(size: 20, color: AppColors.white),
        ),
        actions: [
          Consumer<GradesProvider>(
            builder: (context, gradesProvider, child) {
              return IconButton(
                icon: const Icon(Icons.refresh, color: AppColors.white),
                onPressed: gradesProvider.isLoading
                    ? null
                    : () => gradesProvider.refreshGrades(),
              );
            },
          ),
        ],
      ),
      body: Consumer<GradesProvider>(
        builder: (context, gradesProvider, child) {
          if (gradesProvider.isLoading) {
            return _buildLoadingState();
          }

          if (gradesProvider.error != null) {
            return _buildErrorState(gradesProvider.error!, gradesProvider);
          }

          if (!gradesProvider.hasData || gradesProvider.gradeSummary!.semesters.isEmpty) {
            return _buildEmptyState();
          }

          return _buildGradesContent(gradesProvider.gradeSummary!);
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.loadingGrades.tr(),
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error, GradesProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade400,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.failedToLoadGrades.tr(),
                    style: ThemeManager.semiBold(
                      size: 18,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.replaceAll('Exception: ', ''),
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton.icon(
                    onPressed: () => provider.refreshGrades(),
                    icon: const Icon(Icons.refresh),
                    label: Text(LocaleKeys.tryAgain.tr()),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.grade_outlined,
                    color: AppColors.primary,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.noGradesAvailable.tr(),
                    style: ThemeManager.semiBold(
                      size: 18,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.gradesWillAppear.tr(),
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradesContent(GradeSummary gradeSummary) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Student Info Header
          _buildStudentHeader(gradeSummary.student),
          const SizedBox(height: 24),

          // Overall Performance Card
          _buildOverallPerformanceCard(gradeSummary),
          const SizedBox(height: 24),

          // Semesters List
          ...gradeSummary.semesters.map((semester) =>
            _buildSemesterCard(semester)),
        ],
      ),
    );
  }

  Widget _buildStudentHeader(Student student) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person,
              color: AppColors.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  student.name,
                  style: ThemeManager.semiBold(
                    size: 18,
                    color: AppColors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${LocaleKeys.studentCode.tr()}: ${student.code}',
                  style: ThemeManager.regular(
                    size: 14,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverallPerformanceCard(GradeSummary gradeSummary) {
    final overallPercentage = Provider.of<GradesProvider>(context, listen: false).overallPerformance;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getPerformanceColor(overallPercentage).withValues(alpha: 0.1),
            _getPerformanceColor(overallPercentage).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getPerformanceColor(overallPercentage).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getPerformanceColor(overallPercentage).withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.trending_up,
              color: _getPerformanceColor(overallPercentage),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.overallPerformance.tr(),
                  style: ThemeManager.semiBold(
                    size: 16,
                    color: AppColors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${overallPercentage.toStringAsFixed(1)}%',
                  style: ThemeManager.bold(
                    size: 24,
                    color: _getPerformanceColor(overallPercentage),
                  ),
                ),
                Text(
                  _getPerformanceText(overallPercentage),
                  style: ThemeManager.regular(
                    size: 12,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPerformanceColor(double percentage) {
    if (percentage >= 90) return Colors.green;
    if (percentage >= 80) return Colors.blue;
    if (percentage >= 70) return Colors.orange;
    if (percentage >= 60) return Colors.amber;
    return Colors.red;
  }

  String _getPerformanceText(double percentage) {
    if (percentage >= 90) return LocaleKeys.excellent.tr();
    if (percentage >= 80) return LocaleKeys.veryGood.tr();
    if (percentage >= 70) return LocaleKeys.good.tr();
    if (percentage >= 60) return LocaleKeys.satisfactory.tr();
    return LocaleKeys.needsImprovement.tr();
  }

  Widget _buildSemesterCard(SemesterGrade semester) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Semester Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.school,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        semester.name,
                        style: ThemeManager.semiBold(
                          size: 16,
                          color: AppColors.black,
                        ),
                      ),
                      Text(
                        '${LocaleKeys.semester.tr()} ${semester.semesterNo}',
                        style: ThemeManager.regular(
                          size: 12,
                          color: AppColors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getPerformanceColor(semester.overallPercentage).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${semester.overallPercentage.toStringAsFixed(1)}%',
                    style: ThemeManager.semiBold(
                      size: 12,
                      color: _getPerformanceColor(semester.overallPercentage),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Subjects Table
          _buildSubjectsTable(semester.subjects),
        ],
      ),
    );
  }

  Widget _buildSubjectsTable(List<SubjectGrade> subjects) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.lightGrey.withValues(alpha: 0.3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    LocaleKeys.subject.tr(),
                    style: ThemeManager.semiBold(
                      size: 14,
                      color: AppColors.black,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    LocaleKeys.weekly.tr(),
                    style: ThemeManager.semiBold(
                      size: 14,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    LocaleKeys.finalGrade.tr(),
                    style: ThemeManager.semiBold(
                      size: 14,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    LocaleKeys.overall.tr(),
                    style: ThemeManager.semiBold(
                      size: 14,
                      color: AppColors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // Table Rows
          ...subjects.asMap().entries.map((entry) {
            final index = entry.key;
            final subject = entry.value;
            final isEven = index % 2 == 0;

            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: isEven
                    ? AppColors.white
                    : AppColors.lightGrey.withValues(alpha: 0.1),
                borderRadius: index == subjects.length - 1
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  // Subject Name
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subject.name,
                          style: ThemeManager.semiBold(
                            size: 13,
                            color: AppColors.black,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          subject.code,
                          style: ThemeManager.regular(
                            size: 11,
                            color: AppColors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Weekly Grade
                  Expanded(
                    flex: 2,
                    child: _buildGradeCell(subject.weekQuizzes),
                  ),

                  // Final Grade
                  Expanded(
                    flex: 2,
                    child: _buildGradeCell(subject.finalQuizzes),
                  ),

                  // Overall Grade
                  Expanded(
                    flex: 2,
                    child: _buildOverallGradeCell(subject),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildGradeCell(QuizGrade quizGrade) {
    if (!quizGrade.hasQuizzes) {
      return Column(
        children: [
          Text(
            'N/A',
            style: ThemeManager.regular(
              size: 12,
              color: AppColors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            LocaleKeys.noQuizzes.tr(),
            style: ThemeManager.regular(
              size: 10,
              color: AppColors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return Column(
      children: [
        Text(
          quizGrade.scoreString,
          style: ThemeManager.semiBold(
            size: 12,
            color: AppColors.black,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          quizGrade.percentageString,
          style: ThemeManager.regular(
            size: 10,
            color: _getPerformanceColor(quizGrade.percentage),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOverallGradeCell(SubjectGrade subject) {
    if (!subject.hasAnyGrades) {
      return Column(
        children: [
          Text(
            'N/A',
            style: ThemeManager.regular(
              size: 12,
              color: AppColors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            LocaleKeys.noGrades.tr(),
            style: ThemeManager.regular(
              size: 10,
              color: AppColors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getPerformanceColor(subject.overallPercentage).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            '${subject.overallPercentage.toStringAsFixed(1)}%',
            style: ThemeManager.semiBold(
              size: 11,
              color: _getPerformanceColor(subject.overallPercentage),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
