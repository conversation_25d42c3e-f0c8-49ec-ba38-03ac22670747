import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../../constants/app_colors.dart';
import '../../../../translation/locale_keys.g.dart';
import '../../../../managers/theme_manager.dart';
import '../../widgets/general_item.dart';
import '../single_video_screen.dart';

class VideosTab extends StatefulWidget {
  const VideosTab({
    super.key,
    required this.semesterNo,
    required this.weekNo,
    required this.subject,
  });

  final int weekNo;
  final int semesterNo;
  final WeekSubject subject;

  @override
  State<VideosTab> createState() => _VideosTabState();
}

class _VideosTabState extends State<VideosTab> {
  @override
  Widget build(BuildContext context) {
    // Filter items where itemType is video from all lessons
    final videoItems = widget.subject.lessons
        .expand((lesson) => lesson.items)
        .where((item) => item.itemType == "video")
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (videoItems.isEmpty)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.video_library_outlined,
                      size: 40,
                      color: AppColors.primary.withValues(alpha: 0.6),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    LocaleKeys.noVideoAvailable.tr(),
                    style: ThemeManager.medium(
                      size: 16,
                      color: AppColors.black.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Check back later for video content',
                    style: ThemeManager.regular(
                      size: 14,
                      color: AppColors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else ...[
            // Header section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.video_library,
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Video Lectures',
                          style: ThemeManager.semiBold(
                            size: 18,
                            color: AppColors.black,
                          ),
                        ),
                        Text(
                          '${videoItems.length} video${videoItems.length == 1 ? '' : 's'} available',
                          style: ThemeManager.regular(
                            size: 14,
                            color: AppColors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Video list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: videoItems.length,
              itemBuilder: (context, index) => GeneralItem(
                onTabBackFunction: () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: SingleVideoScreen(
                      title: videoItems[index].title,
                      videoUrl: videoItems[index].itemContent,
                    ),
                    withNavBar: false,
                    pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  );
                },
                title: videoItems[index].title,
                subtitle: 'Video ${index + 1} of ${videoItems.length}',
                icon: Icons.play_circle_filled,
                iconColor: AppColors.white,
                iconBackgroundColor: AppColors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
