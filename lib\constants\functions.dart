import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:arepsalin/constants/variables.dart';
import 'package:arepsalin/data_layer/provider/authentication_provider.dart';
import 'package:arepsalin/data_layer/provider/language_provider.dart';
import 'package:arepsalin/data_layer/provider/locale_provider.dart';
import 'package:arepsalin/data_layer/provider/new_quiz_provider.dart';
import 'package:arepsalin/data_layer/provider/quiz_provider.dart';
import 'package:arepsalin/data_layer/provider/week_provider.dart';
import 'package:arepsalin/data_layer/provider/announcement_provider.dart';
import 'package:arepsalin/data_layer/provider/grades_provider.dart';

import '../data_layer/provider/user_provider.dart';

class Functions {
  Functions._();

  static Locale initialLocale =
      const Locale(Variables.enLangCode, Variables.enCountryCode);

  static List<SingleChildWidget> getProviders() {
    return [
      ChangeNotifierProvider(
          create: (context) => LocaleProvider(
              const Locale(Variables.arLangCode, Variables.enCountryCode))),
      ChangeNotifierProvider(create: (context) => AuthenticationProvider()),
      ChangeNotifierProvider(create: (context) => WeekProvider()),
      ChangeNotifierProvider(create: (context) => QuizProvider()),
      ChangeNotifierProvider(create: (context) => NewQuizProvider()),
      ChangeNotifierProvider(create: (context) => LanguageProvider()),
      ChangeNotifierProvider(create: (context) => UserProvider()),
      ChangeNotifierProvider(create: (context) => AnnouncementProvider()),
      ChangeNotifierProvider(create: (context) => GradesProvider()),
    ];
  }

  Future<void> _showDialog(
      {required BuildContext context,
      required Widget title,
      required Widget content,
      required List<Widget> actions}) {
    return showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: title,
            content: content,
            actions: actions,
          );
        });
  }
}
