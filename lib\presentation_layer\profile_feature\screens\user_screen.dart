import 'package:arepsalin/data_layer/provider/user_provider.dart';
import 'package:arepsalin/managers/secure_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:arepsalin/data_layer/provider/authentication_provider.dart';
import 'package:arepsalin/data_layer/provider/language_provider.dart';
import 'package:arepsalin/managers/shared_pref_manager.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../widgets/custom_image.dart';
import '../../widgets/custom_progress_indicator.dart';
import 'grades_screen.dart';

class UserScreen extends StatefulWidget {
  const UserScreen({Key? key}) : super(key: key);

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  late LanguageProvider langProvider;
  late UserProvider userProvider;

  String getFirstTwoNames(String fullName) {
    List<String> names = fullName.split(" ");

    // Check if there are at least two names
    if (names.length >= 2) {
      String firstName = names[0];
      String secondName = names[1];

      return "$firstName $secondName";
    } else {
      return fullName.isNotEmpty ? fullName : "مستخدم";
    }
  }

  String formatDate(DateTime date) {
    return "${date.day}/${date.month}/${date.year}";
  }

  String? getProfileImageUrl() {
    if (userProvider.student?.user.profilePicture != null &&
        userProvider.student!.user.profilePicture.isNotEmpty) {
      return userProvider.student!.user.profilePicture;
    }
    return null; // Return null to show default avatar
  }

  @override
  void initState() {
    super.initState();
    langProvider = Provider.of(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);

    // Fetch user data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      userProvider.fetchUser();
    });
  }

  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(
          LocaleKeys.userScreen.tr(),
          style: ThemeManager.semiBold(size: 22, color: AppColors.white),
        ),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary,
              AppColors.primary.withOpacity(0.9),
              AppColors.primary.withOpacity(0.7),
              AppColors.warmWhite.withOpacity(0.95),
              AppColors.warmWhite,
            ],
            stops: const [0.0, 0.2, 0.4, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Profile Header Section
              SliverToBoxAdapter(
                child: _buildProfileHeader(size),
              ),
              // Content Section
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([

                    _buildUserInformation(),
                    const SizedBox(height: 20),
                    _buildQuickActions(),
                    const SizedBox(height: 24),
                    _buildSettingsSection(),
                    const SizedBox(height: 24),
                    _buildLogoutButton(),
                    const SizedBox(height: 40),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Profile Header with elegant design
  Widget _buildProfileHeader(Size size) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const SizedBox(height: 20),
          // Profile Picture with enhanced design and tap functionality
          GestureDetector(
            onTap: () => _showProfilePictureDialog(),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.4),
                    Colors.white.withOpacity(0.2),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.25),
                    blurRadius: 25,
                    spreadRadius: 2,
                    offset: const Offset(0, 12),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.15),
                    blurRadius: 50,
                    spreadRadius: 0,
                    offset: const Offset(0, -8),
                  ),
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.1),
                    blurRadius: 30,
                    spreadRadius: 0,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(8),
              child: Consumer<UserProvider>(
                builder: (context, userProvider, child) {
                  if (userProvider.isLoading) {
                    return Container(
                      height: 150,
                      width: 150,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.8),
                          width: 3,
                        ),
                      ),
                      child: const CustomProgressIndicator(),
                    );
                  }

                  final profileImageUrl = getProfileImageUrl();

                  if (profileImageUrl != null && profileImageUrl.isNotEmpty) {
                    return Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.9),
                          width: 4,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: CustomImage(
                          addRadius: true,
                          image: profileImageUrl,
                          fromAssets: false,
                          radius: 75,
                          height: 150,
                          width: 150,
                        ),
                      ),
                    );
                  } else {
                    return Container(
                      height: 150,
                      width: 150,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            Colors.white.withOpacity(0.95),
                          ],
                        ),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.9),
                          width: 4,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.person,
                        size: 80,
                        color: AppColors.primary.withOpacity(0.7),
                      ),
                    );
                  }
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          // User Name with elegant typography
          Consumer<UserProvider>(
            builder: (context, userProvider, child) {
              if (userProvider.isLoading) {
                return Container(
                  height: 24,
                  width: 150,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                );
              }

              if (userProvider.student == null) {
                return Text(
                  "مستخدم",
                  style: ThemeManager.bold(
                    size: 24,
                    color: Colors.white,
                  ),
                );
              }

              return Text(
                getFirstTwoNames(userProvider.student!.user.name),
                style: ThemeManager.bold(
                  size: 24,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              );
            },
          ),
          const SizedBox(height: 8),
          // User Status Badge
          Consumer<UserProvider>(
            builder: (context, userProvider, child) {
              if (userProvider.student?.isVerified == true) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.green.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.verified,
                        color: Colors.green.shade300,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        "تم التحقق",
                        style: ThemeManager.medium(
                          size: 12,
                          color: Colors.green.shade300,
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  // Quick Actions Section
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withOpacity(0.2),
                      AppColors.primary.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.dashboard_outlined,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                "الإجراءات السريعة",
                style: ThemeManager.bold(
                  size: 18,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildActionTile(
            icon: Icons.grade,
            title: LocaleKeys.grades.tr(),
            subtitle: "عرض الدرجات والنتائج",
            onTap: () {
              // Navigate to grades
            },
          ),
        ],
      ),
    );
  }

  // User Information Section
  Widget _buildUserInformation() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        if (userProvider.student == null) {
          return const SizedBox.shrink();
        }

        final student = userProvider.student!;
        final user = student.user;

        return Column(
          children: [
            // Personal Information Card
            _buildInfoCard(
              title: "المعلومات الشخصية",
              icon: Icons.person_outline,
              children: [
                _buildInfoRow("الاسم الكامل", user.name),
                _buildInfoRow("البريد الإلكتروني", user.email),
                _buildInfoRow("تاريخ الميلاد", formatDate(user.birthday)),
                _buildInfoRow("الجنسية", user.nationality),
                _buildInfoRow("العنوان", user.address),
                _buildInfoRow("الجنس", user.gender == 'male' ? 'ذكر' : 'أنثى'),
              ],
            ),
            const SizedBox(height: 20),
            // Student Information Card
            _buildInfoCard(
              title: "معلومات الطالب",
              icon: Icons.school_outlined,
              children: [
                _buildInfoRow("كود الطالب", student.studentCode),
                _buildInfoRow("المدينة", student.city),
                _buildInfoRow("الكنيسة", student.church),
                _buildInfoRow("أب الاعتراف", student.abEle3traf),
                _buildInfoRow("مستوى الشماسية", student.deaconLevel),
                _buildInfoRow("خدمة الكنيسة", student.churchService),
                _buildInfoRow("المؤهلات", student.qualifications),
              ],
            ),
          ],
        );
      },
    );
  }

  // Settings Section
  Widget _buildSettingsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withOpacity(0.2),
                      AppColors.primary.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.settings_outlined,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                "الإعدادات",
                style: ThemeManager.bold(
                  size: 18,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildLanguageSelector(),
        ],
      ),
    );
  }

  // Logout Button
  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red.shade400,
            Colors.red.shade600,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            _showLogoutDialog();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.logout_rounded,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  LocaleKeys.logOut.tr(),
                  style: ThemeManager.bold(
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          )
        ],
        borderRadius: BorderRadius.circular(16),
        color: AppColors.white,
        border: Border.all(
          color: AppColors.primary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: ThemeManager.semiBold(
                  size: 18,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: ThemeManager.medium(
                size: 14,
                color: AppColors.grey,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: Text(
              value.isNotEmpty ? value : "غير محدد",
              style: ThemeManager.semiBold(
                size: 14,
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Action Tile Helper
  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.warmWhite.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: ThemeManager.semiBold(
                          size: 16,
                          color: AppColors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: ThemeManager.regular(
                          size: 14,
                          color: AppColors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.grey,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Language Selector
  Widget _buildLanguageSelector() {
    return Consumer<LanguageProvider>(
      builder: (context, langProvider, child) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.warmWhite.withOpacity(0.5),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.language,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            LocaleKeys.lang.tr(),
                            style: ThemeManager.semiBold(
                              size: 16,
                              color: AppColors.black,
                            ),
                          ),
                        ),
                        Icon(
                          isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: AppColors.grey,
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (isExpanded) ...[
                Container(
                  height: 1,
                  color: AppColors.primary.withOpacity(0.1),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                ),
                _buildLanguageOption(
                  "اللغة العربية",
                  "ar",
                  langProvider.currentLanguage == "ar",
                  () => langProvider.setLanguage(context, "ar"),
                ),
                _buildLanguageOption(
                  "English",
                  "en",
                  langProvider.currentLanguage == "en",
                  () => langProvider.setLanguage(context, "en"),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  // Language Option
  Widget _buildLanguageOption(
    String title,
    String code,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const SizedBox(width: 40), // Align with icon above
              Expanded(
                child: Text(
                  title,
                  style: ThemeManager.medium(
                    size: 15,
                    color: isSelected ? AppColors.primary : AppColors.grey,
                  ),
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppColors.primary,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Logout Dialog
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout_rounded,
                color: Colors.red.shade400,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                "تسجيل الخروج",
                style: ThemeManager.bold(
                  size: 18,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          content: Text(
            "هل أنت متأكد من أنك تريد تسجيل الخروج؟",
            style: ThemeManager.regular(
              size: 16,
              color: AppColors.grey,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                "إلغاء",
                style: ThemeManager.medium(
                  size: 14,
                  color: AppColors.grey,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performLogout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                "تسجيل الخروج",
                style: ThemeManager.medium(
                  size: 14,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Show Profile Picture Dialog
  void _showProfilePictureDialog() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final profileImageUrl = getProfileImageUrl();

    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Stack(
            children: [
              // Main image container
              Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    maxHeight: MediaQuery.of(context).size.height * 0.7,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 30,
                        spreadRadius: 0,
                        offset: const Offset(0, 15),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: profileImageUrl != null && profileImageUrl.isNotEmpty
                        ? CustomImage(
                            image: profileImageUrl,
                            fromAssets: false,
                            addRadius: false,
                            width: double.infinity,
                            height: double.infinity,
                          )
                        : Container(
                            width: 300,
                            height: 300,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white,
                                  Colors.white.withOpacity(0.95),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.person,
                                  size: 120,
                                  color: AppColors.primary.withOpacity(0.7),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  "لا توجد صورة شخصية",
                                  style: ThemeManager.medium(
                                    size: 16,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                ),
              ),
              // Close button
              Positioned(
                top: 40,
                right: 20,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
              // User info overlay
              if (userProvider.student != null)
                Positioned(
                  bottom: 40,
                  left: 20,
                  right: 20,
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          getFirstTwoNames(userProvider.student!.user.name),
                          style: ThemeManager.bold(
                            size: 20,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        if (userProvider.student!.isVerified)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.green.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.verified,
                                  color: Colors.green.shade300,
                                  size: 16,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  "تم التحقق",
                                  style: ThemeManager.medium(
                                    size: 12,
                                    color: Colors.green.shade300,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  // Perform Logout
  void _performLogout() {
    // Add logout logic here
    // Navigate to login screen
    // Clear tokens
  }
}
