import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:arepsalin/constants/app_images.dart';
import 'package:no_screenshot/no_screenshot.dart';

import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';
import 'package:provider/provider.dart';

import '../../../constants/app_colors.dart';

import '../../../data_layer/model/user_model.dart';
import '../../../data_layer/provider/user_provider.dart';
import '../../about_us/screens/about_us_screen.dart';
import '../../course_feature/screens/years_screen.dart';
import '../widgets/home_item.dart';
import 'package:arepsalin/data_layer/provider/announcement_provider.dart';
import 'package:arepsalin/presentation_layer/home_feature/widgets/announcement_carousel.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _noScreenshot = NoScreenshot.instance;

  @override
  void initState() {
    super.initState();
    // Fetch announcements when the screen loads
    _ensureScreenshotsEnabled();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AnnouncementProvider>(context, listen: false).fetchAnnouncements();
    });

  }

  Future<void> _ensureScreenshotsEnabled() async {
    try {
      await _noScreenshot.screenshotOn();
      debugPrint('Screenshots enabled in HomeScreen');
    } catch (e) {
      debugPrint('Error enabling screenshots: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: AppColors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ));
    final userProvider = Provider.of<UserProvider>(context);
    final announcementProvider = Provider.of<AnnouncementProvider>(context);
    final student = userProvider.student;
    Size size = MediaQuery.sizeOf(context);
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: Stack(
            children: [
              SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: Image(
                    image: AssetImage(AppImages.backgroundImage),
                    fit: BoxFit.fill,
                  )),
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.only(
                      right: 15, left: 15, top: 25, bottom: 35),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 14),
                        decoration: BoxDecoration(
                            border: Border.all(color: AppColors.bermuda),
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(10)),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.control_camera_outlined,
                              size: 22,
                              color: AppColors.bermuda,
                            ),
                            const SizedBox(width: 5),
                            userProvider.isLoading?const Center(child: CircularProgressIndicator())
                            :Text(
                              '${LocaleKeys.welcome.tr()}${student?.user.name}', //todo
                              style: ThemeManager.semiBold(
                                  size: 20, color: AppColors.black),
                            ),
                          ],
                        ),
                       ),
                        const SizedBox(height: 5),
                        Container(
                          height: size.height / 5.1,
                          margin:
                              const EdgeInsetsDirectional.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color: AppColors.transparent,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            margin: const EdgeInsetsDirectional.only(top: 20),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 14, vertical: 0),
                            decoration: BoxDecoration(
                                boxShadow: const [
                                  BoxShadow(
                                    color: AppColors.bermuda,
                                    offset: Offset(5, 5),
                                    blurRadius: 10,
                                    spreadRadius: 0.1,
                                  )
                                ],
                                border: Border.all(color: AppColors.bermuda),
                                color: AppColors.white,
                                borderRadius: BorderRadius.circular(10)),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        'ليَمْتَلِئُ فَمِي مِنْ تَسْبِيحِكَ، الْيَوْمَ كُلَّهُ مِنْ مَجْدِكََّ. (اَلْمَزَامِيرُ ٨:٧١)',
                                        maxLines: 4,
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.visible,
                                        softWrap: true,
                                        style: ThemeManager.semiBold(
                                            size: 20, color: AppColors.black),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    AnnouncementCarousel(
                        announcements: announcementProvider.announcements,
                        isLoading: announcementProvider.isLoading,
                        error: announcementProvider.error,
                      ),
                      InkWell(
                        onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AboutUsScreen(),
                            )),
                        child: Container(
                          margin: EdgeInsetsDirectional.symmetric(
                              vertical: 15,
                              horizontal:
                                  MediaQuery.sizeOf(context).width * 0.2),
                          width: MediaQuery.sizeOf(context).width * 0.4,
                          padding: const EdgeInsetsDirectional.symmetric(
                              vertical: 15),
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            border: Border.all(color: AppColors.white),
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withOpacity(0.5),
                                offset: const Offset(5, 5), // Adjust the offset
                                blurRadius: 5, // Adjust the blur radius
                                spreadRadius: 0.1, // Adjust the spread radius
                              )
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.info_outline_rounded,
                                color: AppColors.white,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                LocaleKeys.aboutUs.tr(),
                                style: ThemeManager.semiBold(
                                  color: AppColors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          HomeItem(
                              title: LocaleKeys.euchologion.tr(),
                              image: AppImages.odas,
                              onPressed: () {}),
                          HomeItem(
                              title: LocaleKeys.courses.tr(),
                              image: AppImages.books,
                              onPressed: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const YearsScreen(),
                                    ));
                              }),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          HomeItem(
                              title: LocaleKeys.psalmody.tr(),
                              image: AppImages.kiahk,
                              onPressed: () {}),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
