import 'package:flutter/material.dart';
import 'package:arepsalin/data_layer/model/grade_model.dart';
import 'package:arepsalin/data_layer/service/grades_service.dart';

class GradesProvider extends ChangeNotifier {
  final GradesService _gradesService = GradesService();
  
  GradeSummary? _gradeSummary;
  bool _isLoading = false;
  String? _error;

  // Getters
  GradeSummary? get gradeSummary => _gradeSummary;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _gradeSummary != null;

  // Fetch grades
  Future<void> fetchGrades() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _gradeSummary = await _gradesService.getStudentGrades();
      _error = null;
    } catch (e) {
      _error = e.toString();
      _gradeSummary = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh grades
  Future<void> refreshGrades() async {
    await fetchGrades();
  }

  // Clear data
  void clearData() {
    _gradeSummary = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Get semester by number
  SemesterGrade? getSemesterByNumber(int semesterNo) {
    if (_gradeSummary == null) return null;
    
    try {
      return _gradeSummary!.semesters
          .firstWhere((semester) => semester.semesterNo == semesterNo);
    } catch (e) {
      return null;
    }
  }

  // Get overall student performance
  double get overallPerformance {
    if (_gradeSummary == null || _gradeSummary!.semesters.isEmpty) return 0.0;
    
    double totalPercentage = 0.0;
    int semestersWithGrades = 0;
    
    for (var semester in _gradeSummary!.semesters) {
      if (semester.subjects.any((subject) => subject.hasAnyGrades)) {
        totalPercentage += semester.overallPercentage;
        semestersWithGrades++;
      }
    }
    
    return semestersWithGrades > 0 ? totalPercentage / semestersWithGrades : 0.0;
  }
}
