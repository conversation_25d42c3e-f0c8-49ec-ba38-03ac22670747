import 'package:arepsalin/data_layer/model/week_response.dart';
import 'package:arepsalin/data_layer/model/week_subject_model.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:arepsalin/managers/theme_manager.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/tabs/audios_tab.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/tabs/content_tab.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/tabs/new_tests_tab.dart';
import 'package:arepsalin/presentation_layer/course_feature/screens/tabs/videos_tab.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_images.dart';
import '../../../data_layer/model/week_model.dart';
import '../../../translation/locale_keys.g.dart';

class LessonsScreen extends StatelessWidget {
  LessonsScreen(
      {super.key,
      required this.title,
      required this.week,
      required this.semesterNo,
      required this.subject});

  final String title;
  final WeekResponse week;
  final int semesterNo;
  final WeekSubject subject;
  List<String> options = [
    LocaleKeys.content.tr(),
    LocaleKeys.videos.tr(),
    LocaleKeys.audios.tr(),
    LocaleKeys.tests.tr(),
  ];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: AppBar(
          foregroundColor: AppColors.warmWhite,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: AppColors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.light,
          ),
          title: Text(title,
              style: ThemeManager.medium(color: AppColors.white, size: 22)),
          centerTitle: true,
          backgroundColor: AppColors.primary,
          bottom: TabBar(
              enableFeedback: false,
              labelColor: AppColors.bermuda,
              unselectedLabelColor: AppColors.warmWhite.withOpacity(0.7),
              indicatorColor: AppColors.bermuda,
              labelStyle: ThemeManager.semiBold(size: 16),
              tabs: [
                Tab(
                  icon: const Icon(Icons.my_library_books_sharp),
                  text: LocaleKeys.content.tr(),
                ),
                Tab(
                  icon: const Icon(Icons.video_library_outlined),
                  text: LocaleKeys.videos.tr(),
                ),
                Tab(
                  icon: const Icon(Icons.audiotrack_outlined),
                  text: LocaleKeys.audios.tr(),
                ),
                Tab(
                  icon: const Icon(Icons.quiz_outlined),
                  text: LocaleKeys.tests.tr(),
                ),
              ]),
        ),
        body: Stack(
          children: [
            SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Image(
                  image: AssetImage(AppImages.backgroundImage),
                  fit: BoxFit.fill,
                )),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 15),
              child: TabBarView(children: [
                ContentTab(
                    semesterNo: semesterNo, weekNo: week.id, subject: subject),
                VideosTab(
                    semesterNo: semesterNo, weekNo: week.id, subject: subject),
                AudiosTab(
                    semesterNo: semesterNo, weekNo: week.id, subject: subject),
                NewTestsTab(
                  lessonId:
                      subject.lessons.isNotEmpty ? subject.lessons.first.id : 1,
                )
              ]),
            ),
          ],
        ),
      ),
    );
  }
}
