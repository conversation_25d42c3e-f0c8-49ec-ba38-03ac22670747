import 'lesson_item_model.dart';

class Lesson {
  final int id;
  final String name;
  final List<LessonItem> items;
  final int order;

  Lesson({
    required this.id,
    required this.name,
    required this.items,
    required this.order,
  });

  factory Lesson.fromJson(Map<String, dynamic> json) {
    return Lesson(
      id: json['id'],
      name: json['name'],
      items: (json['items'] as List)
          .map((item) => LessonItem.fromJson(item))
          .toList(),
      order: json['order'],
    );
  }
}